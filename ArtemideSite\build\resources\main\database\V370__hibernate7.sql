# Remove the article without sap code
delete from ArticoloDescrizione where Articolo_id=4656909;
delete from Articolo where id=4656909;

# Add the new types and constraints
ALTER TABLE Articolo MODIFY codiceSap varchar(255) NOT NULL;
ALTER TABLE ArticoloDescrizione MODIFY value LONGTEXT;
ALTER TABLE CustomButtonLink MODIFY value LONGTEXT;
ALTER TABLE CustomButtonName MODIFY value LONGTEXT;
ALTER TABLE Designer MODIFY surname varchar(50) NOT NULL;
ALTER TABLE DesignerDescription MODIFY value LONGTEXT;
ALTER TABLE ElectricalName MODIFY value LONGTEXT;
ALTER TABLE FamigliaNome MODIFY value LONGTEXT;
ALTER TABLE FilterVarianteDescription MODIFY value LONGTEXT;
ALTER TABLE GalleryDescriptionText MODIFY value LONGTEXT;
ALTER TABLE PdfData MODIFY frontView LONGTEXT;
ALTER TABLE PdfData MODIFY jsonConnections LONGTEXT;
ALTER TABLE PdfData MODIFY jsonPowerOptions LONGTEXT;
ALTER TABLE PdfData MODIFY perspectiveView LONGTEXT;
ALTER TABLE PdfData MODIFY sideView LONGTEXT;
ALTER TABLE PdfData MODIFY topView LONGTEXT;
ALTER TABLE Prodotto MODIFY codice varchar(255) NOT NULL;
ALTER TABLE ProdottoBallast MODIFY value LONGTEXT;
ALTER TABLE ProdottoBaseColor MODIFY value LONGTEXT;
ALTER TABLE ProdottoColor MODIFY value LONGTEXT;
ALTER TABLE ProdottoDescription MODIFY value LONGTEXT;
ALTER TABLE ProdottoElectricalBallast MODIFY value LONGTEXT;
ALTER TABLE ProdottoElectricalEmergency MODIFY value LONGTEXT;
ALTER TABLE ProdottoElectricalName MODIFY value LONGTEXT;
ALTER TABLE ProdottoElectricalRemoteControl MODIFY value LONGTEXT;
ALTER TABLE ProdottoElectricalTransformer MODIFY value LONGTEXT;
ALTER TABLE ProdottoEmergency MODIFY value LONGTEXT;
ALTER TABLE ProdottoMaterial MODIFY value LONGTEXT;
ALTER TABLE ProdottoName MODIFY value LONGTEXT;
ALTER TABLE ProdottoNote MODIFY value LONGTEXT;
ALTER TABLE ProdottoRemoteControl MODIFY value LONGTEXT;
ALTER TABLE ProdottoShortName MODIFY value LONGTEXT;
ALTER TABLE ProdottoSubName MODIFY value LONGTEXT;
ALTER TABLE ProdottoTransformer MODIFY value LONGTEXT;
ALTER TABLE ProductConfiguration MODIFY globalOptions LONGTEXT;
ALTER TABLE ProductConfiguration MODIFY scene LONGTEXT;
ALTER TABLE Property MODIFY value LONGTEXT;
ALTER TABLE PublicationsLink MODIFY value LONGTEXT;
ALTER TABLE PublicationsSubtitle MODIFY value LONGTEXT;
ALTER TABLE PublicationsTitle MODIFY value LONGTEXT;
ALTER TABLE Store MODIFY nome varchar(255) NOT NULL;
ALTER TABLE SubfamilyConfigText MODIFY value LONGTEXT;
ALTER TABLE SubfamilyCutoutShape MODIFY value LONGTEXT;
ALTER TABLE SubfamilyEmission MODIFY value LONGTEXT;
ALTER TABLE SubfamilyEnvironment MODIFY value LONGTEXT;
ALTER TABLE SubfamilyName MODIFY value LONGTEXT;
ALTER TABLE UploadedFileDescription MODIFY value LONGTEXT;
ALTER TABLE UploadedFileTitle MODIFY value LONGTEXT;
ALTER TABLE VarianteDescription MODIFY value LONGTEXT;
ALTER TABLE YadaClause MODIFY content LONGTEXT;
ALTER TABLE PageModule ADD CONSTRAINT chk_pagemodule_dtype CHECK (DTYPE in ('PageModule','HomeModule','NewsModule','ProjectModule'));
ALTER TABLE PageModule ADD CONSTRAINT chk_pagemodule_year CHECK (DTYPE <> 'ProjectModule' or (year is not null));
ALTER TABLE Tag ADD CONSTRAINT chk_tag_dtype CHECK (DTYPE in ('Tag','NewsTag','ProjectTag'));
ALTER TABLE YadaUserProfile ADD CONSTRAINT chk_yadauserprofile_dtype CHECK (DTYPE in ('YadaUserProfile','UserProfile'));