buildscript {
	dependencies {
		classpath 'net.yadaframework:yadatools:0.7.7.R4' 
        classpath files("../../yadaframework/YadaTools/lib/gradle-js-plugin-2.14.1-x.jar")
	}                    
}
plugins {
	id 'org.hidetake.ssh' version '2.12.0'
	id 'com.meiuwa.gradle.sass' version "1.0.0"
	id "com.eriwen.gradle.js" version "2.14.1"
	id("com.github.node-gradle.node") version "7.1.0"
}

ext.acronym = 'amd'
apply plugin: 'java-library'
apply plugin: 'war'
apply plugin: 'eclipse-wtp'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}
subprojects {
    apply plugin: 'java'

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
}
compileJava.options.encoding = 'UTF-8'
compileTestJava.options.encoding = 'UTF-8'

tasks.withType(JavaCompile) {
	options.encoding = 'UTF-8'
}

tasks.withType(JavaCompile).configureEach {
    // Needed since Spring 6.1: 
    // https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention
    options.compilerArgs.add("-parameters")
}

eclipse {
	// jdt {
	//	sourceCompatibility = 1.8
	//	targetCompatibility = 1.8
	// }
	classpath {
        downloadJavadoc = true
        downloadSources = true
    }
}

def YadaWebLib = "$projectDir/../../yadaframework/YadaWeb";
def YadaWebCmsLib = "$projectDir/../../yadaframework/YadaWebCMS";
// Questo funziona solo se i due git "Responsive" e "configurator" stanno nella stessa directory
def configuratorDir = "$projectDir/../../configurator/ArtemideConfigurator"
def configuratorBDir = "$projectDir/../../configurator/ArtemideConfiguratorB"
def dxfConverterProjectDir = "$projectDir/../../dxf-converter"
def dxfConverterBinDir = "$dxfConverterProjectDir/DxfExporter/bin/Production/net6.0"

repositories {
	mavenCentral()
}

dependencies {
	implementation   project(':YadaWeb')
	implementation	 project(':YadaWebSecurity')
	implementation	 project(':YadaWebCMS')
	implementation	 project(':ArtemideCommon')
	
	implementation 'org.springframework.data:spring-data-jpa:3.5.3',
		'org.springframework.data:spring-data-commons:3.5.3',
		// 'org.springframework:spring-webmvc:5.+',
		// 'org.springframework:spring-context-support:5.+',
		'org.springframework.social:spring-social-facebook:2.0.3.RELEASE',
		'org.flywaydb:flyway-mysql:10.22.0', // 10 is compiled with jdk17
		// 'org.springframework.security:spring-security-web:5.+',
		// 'org.springframework.security:spring-security-core:5.+',
		// 'org.hibernate:hibernate-entitymanager:5.3.+',
		// 'org.hibernate:hibernate-validator:5.4.2.Final',
		// 'org.thymeleaf:thymeleaf-spring5:3.1.2.RELEASE',
		// 'org.apache.tomcat:tomcat-servlet-api:8.5.83',
		// 'com.fasterxml.jackson.core:jackson-annotations:2.13.4',
		// 'com.fasterxml.jackson.core:jackson-databind:2.13.4',
		// 'ch.qos.logback:logback-classic:1.3.+',
		'org.apache.commons:commons-collections4:4.5.0-M2',
		'org.apache.commons:commons-io:1.3.2',
		'net.sourceforge.jexcelapi:jxl:2.6.12',
		'org.apache.poi:poi:5.4.1',
		'org.apache.poi:poi-ooxml:5.4.1',
		'com.google.guava:guava:32.1.2-jre',
		'net.lingala.zip4j:zip4j:2.7.0',
		'com.google.api-client:google-api-client:2.7.0',
		// 'com.mysql:mysql-connector-j:8.1.+',
		'ru.yandex.qatools.ashot:ashot:1.5.4'
		//'com.google.http-client:google-http-client-gson:1.+'
		// 'com.google.api-client:google-api-client-jackson2:2.+'
		// 'com.google.api-client:google-api-client:1.23.+',
		// org.springframework.social.UncategorizedApiException: (#12) bio field is deprecated for versions v2.8 and higher
		// 'org.springframework.social:spring-social-facebook:3.0.+';

	// Percy and selenium for visual testing
    implementation 'io.percy:percy-java-selenium:2.1.0'
    // implementation 'org.seleniumhq.selenium:selenium-chrome-driver:4.+'
    // implementation 'org.seleniumhq.selenium:selenium-support:4.+'
    // This must match the locally installed version of Chrome
    implementation 'org.seleniumhq.selenium:selenium-devtools-v134:4.32.0' // for COL
    implementation 'org.seleniumhq.selenium:selenium-devtools-v139:4.35.0' // for DEV
    implementation 'org.seleniumhq.selenium:selenium-java:4.35.0'
		
	// Add here any library that you might need
	// compile 'org.apache.commons:commons-lang3:3.5'
	
	// compile	'joda-time:joda-time:2.+'
	
	// Fixes commons-text:1.9 CVE-2022-42889 caused by commons-configuration2:2.8.0
	implementation 'org.apache.commons:commons-text:1.12.0'

    // testCompile 'junit:junit:4.12'

    // Needed to run in Tomcat
	runtimeOnly 'commons-beanutils:commons-beanutils:1.11.0'
	runtimeOnly 'commons-jxpath:commons-jxpath:1.3'

	compileOnly 'jakarta.servlet:jakarta.servlet-api:6.1.0'
}

configurations {
	// Fix for Java4Shell vulnerability (removing log4j v1 referenced by jxl)
    all*.exclude group: 'log4j', module: 'log4j'
	// Fix Spring logging conflict: exclude commons-logging in favor of spring-jcl
    all*.exclude group: 'commons-logging', module: 'commons-logging'
}

def getTimestamp() {
    new Date().format('yyyyMMddHHmmssSSS')
}

task listJars {
	doLast {
		configurations.runtimeClasspath.each { File file -> println file.name }
	}
}

apply plugin: 'net.yadaframework.tools'

task cleanCss {
	doLast {
		delete fileTree(dir: "src/main/webapp/res/css", include: "**/*.min.*")
	}
}

task("home2SassCompile", type: com.meiuwa.gradle.sass.SassTask) {
	inputs.dir("src/main/webapp/res/css/modules")
	inputs.file("src/main/webapp/res/css/home2.scss")
	outputs.file("src/main/webapp/res/css/home2.min.css")    
    source = project.files(fileTree("src/main/webapp/res/css")) 
    include("home2.scss")
    output = file("src/main/webapp/res/css")
}

task("mainSassCompile", type: com.meiuwa.gradle.sass.SassTask) {
	inputs.files(
		fileTree("src/main/webapp/res/css") {include("**/*.scss")}, 
		fileTree("$YadaWebLib/src/main/resources/net/yadaframework/views/yada/css"), 
		fileTree("$YadaWebCmsLib/src/main/resources/net/yadaframework/views/yada/css")
	) 
	outputs.files(fileTree("src/main/webapp/res/css") {
        include '**/*.min.css'
    })
    source = project.files(fileTree("src/main/webapp/res/css"), fileTree("$YadaWebLib/src/main/resources/net/yadaframework/views/yada/css"), fileTree("$YadaWebCmsLib/src/main/resources/net/yadaframework/views/yada/css")) 
    include("**/*.sass", "**/*.scss", "**/*.css")
    exclude("**/*.min.*", "**/_*.sass", "**/_*.scss")
    output = file("src/main/webapp/res/css")
}
task("configuratorSassCompile", type: com.meiuwa.gradle.sass.SassTask) {
	// CSS solo in Configurator non ConfiguratorB perché non ha file scss
    inputs.files(
        fileTree("$configuratorDir/css") {include("**/*.scss")}
    ) 
    outputs.files(fileTree("$configuratorDir/css") {
        include '**/*.min.css'
    })
    source = project.files(fileTree("$configuratorDir/css")) 
    include("**/*.sass", "**/*.scss", "**/*.css")
    exclude("**/*.min.*", "**/_*.sass", "**/_*.scss")
    output = file("$configuratorDir/css")
}

eclipse {
	jdt {
		sourceCompatibility = 21
		targetCompatibility = 21
	}
    classpath {
        downloadJavadoc = true
        downloadSources = true
    }
	wtp {
    	component {
    		// Set the needed context path here
			contextPath = '/'
		}
		facet {
			facet name: 'jst.web', version: '3.1'
			def oldJstWebFacet = facets.findAll {
                it.name == 'jst.web' && it.version == '2.4'
            }
            facets.removeAll(oldJstWebFacet)
		}
	}
	// https://blog.gradle.org/buildship-sync-task-exec
    autoBuildTasks mainSassCompile, configuratorSassCompile
}

yadaInit {
	projectName = rootProject.name
	acronym = project.acronym
	basePackage = 'com.artemide'
	dbPasswords = ['dev': 'qweqwe', 'col': 'qweqwe', 'prod': 'tgeestbe4']
	envs=['dev', 'col', 'prod'];
}

configurations {
    generateSchema {
		canBeResolved = true
		canBeConsumed = false
	}
}

dependencies {
    generateSchema 'org.apache.logging.log4j:log4j-core:2.25.1'
    generateSchema 'org.glassfish:jakarta.el:5.0.0-M1'
}

task dbSchema(dependsOn: [classes], type: JavaExec) {
	inputs.files (configurations.generateSchema, "$rootDir/src/main/resources/META-INF/persistence.xml") 
	outputs.files "$rootDir/schema/${acronym}.sql" // This must be the same name used in persistence.xml
    classpath = configurations.generateSchema + sourceSets.main.runtimeClasspath
    mainClass = 'net.yadaframework.tools.YadaSchemaGenerator'
    doFirst {
        delete outputs.files
		// Since recently, for autodiscovery of entities to work, 
		// the compiled entities must be in the root folder of the META-INF/persistence.xml file
		// so I copy the META-INF file there:
        copy {
     		from 'src/main/resources/META-INF'
    		into layout.buildDirectory.dir('classes/java/main/META-INF')
        }
    }
    doLast {
        delete layout.buildDirectory.dir('classes/java/main/META-INF')
    }
}
// dbSchema.finalizedBy(deleteMetaInf)

sass {
	properties = "$rootDir/sass.properties"
    download {
		version = "1.23.1"
		output = "$rootDir/.sass"
    }
}




/////////////////////////////////////////////////////////////////////////////////////////
// Deploy
/////////////////////////////////////////////////////////////////////////////////////////
// import groovy.swing.SwingBuilder // Removed for Gradle 9 compatibility

apply plugin: "org.hidetake.ssh" 		// https://gradle-ssh-plugin.github.io

//if (!project.hasProperty('env')) {
//	throw new BuildCancelledException("Missing 'env' variable: you need to define the env variable on the command line, for example 'gradle -Penv=prod deploy'")
//}
//if (!project.hasProperty('acronym')) {
//	throw new BuildCancelledException("Missing 'acronym' variable: you need to define the acronym variable in the script, for example 'ext.acronym = \"abc\"'")
//}

if (!project.hasProperty('env')) {
	ext.env="UNSET"
}
def startTime = new Date()
def workDir = "${layout.buildDirectory.get().asFile}/tmp/yadabuild"
def timestamp = startTime.format('yyyyMMdd@HHmm')
// Cambia "amdprod18" e "amdprod20" in "amdprod"
def acroenv = "${acronym}${env}".replaceFirst("prod18", "prod").replaceFirst("prod20", "prod")
def remoteWarName = "${acronym}.[${timestamp}].war"
def localWarName = remoteWarName
def warPath = "${layout.buildDirectory.get().asFile}/libs/$localWarName"
// def assetsDir = "${workDir}/assets"
def assetsConfiguratorDir = "${workDir}/assetsConfigurator"
def zipNameSuffix = "${acronym}.[${timestamp}].zip"
def config = new ConfigSlurper(env).parse(new File("$projectDir/deploy.config").toURI().toURL())
// def tomcatbase = "/srv/${acroenv}/tomcat"
def tomcatbase = config.server.tomcatbase

// ant rimosso: ant.importBuild "$projectDir/ant/yadabuild.xml"

// ssh plugin
remotes {
	deployHost {
		host = config.server.host
		user = config.server.user
		identity = config.server.identity
		knownHosts = addHostKey(file("known_hosts"))
		jschLog=true
	}
}

ssh.settings {
	jschLog = true
}

println "acronym=${acronym}"
println "env=${env}"
println "acroenv=${acroenv}"
println "targetHost=${config.server.host}"
println ""

/**
 * Opens a confirmation dialog
 * @param prompt
 * @return
 */
def confirm(String prompt) {
	// Console-based confirmation for Gradle 9 compatibility (SwingBuilder removed)
	def yn = System.console()?.readLine(prompt + " (y/N): ")
	if (yn == null) {
		// Fallback when no console available (IDE execution)
		println prompt + " (y/N): "
		yn = System.in.newReader().readLine()
	}
	return yn?.toLowerCase() == "y"
}

task incrementBuild(type: JavaExec) {
	classpath = sourceSets.main.runtimeClasspath
	mainClass = 'net.yadaframework.tools.AntIncrementBuild'
	args "$projectDir/src/main/webapp/WEB-INF/build.properties"
}

task compileConfiguratorAolProd(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-prod-aol"
}

task compileConfiguratorAolDev(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-aol"
}

task compileConfiguratorFnvProd(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-prod-fnv"
}

task compileConfiguratorFnvDev(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-fnv"
}

task compileConfiguratorTaProd(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-prod-ta"
}

task compileConfiguratorTaDev(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-ta"
}

task compileConfiguratorHoyProd(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-prod-hoy"
}

task compileConfiguratorHoyDev(type: Exec) { 
	workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-hoy"
}


task compileConfiguratorA24Prod(type: Exec) { 
    workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-prod-a24"
}

task compileConfiguratorA24Dev(type: Exec) { 
    workingDir configuratorBDir
    commandLine "cmd", "/c", "npm", "run", "build-a24"
}

// Legacy A.24
task compileConfiguratorV1Dev(type: Exec) { 
    workingDir configuratorDir
    commandLine "cmd", "/c", "npm", "run", "build"
}

// Legacy A.24
task compileConfiguratorV1Prod(type: Exec) { 
    workingDir configuratorDir
    commandLine "cmd", "/c", "npm", "run", "build-prod"
}

task compileConfiguratorAllV2(dependsOn: [
    compileConfiguratorHoyDev, compileConfiguratorHoyProd,
    compileConfiguratorTaDev, compileConfiguratorTaProd,
    compileConfiguratorFnvDev, compileConfiguratorFnvProd,
    compileConfiguratorA24Dev, compileConfiguratorA24Prod,
    compileConfiguratorAolDev, compileConfiguratorAolProd
    ]) {
    doLast {
        // Nothing to do
    }
}

// ant rimosso: ant.properties['assetsDir'] = assetsDir

// js applicativi da minimizzare
def appJsInputFiles = project.files(fileTree(dir: "${webAppDir}/res/js", include: '**/*.js', exclude: ['*.min.*', '**/yada*.js']))
// destinazione dei js applicativi minimizzati 
def appJsOutputFolder = file("${webAppDir}/res/js/")

def defaultMinifyOptions = {
    // warningLevel = 'QUIET'
	compilerOptions.languageIn = "ECMASCRIPT6"
	compilerOptions.languageOut = "ECMASCRIPT5"
}

/**
 * OLD Crea i task dinamici che minimizzano i js applicativi singolarmente
 */
appJsInputFiles.eachWithIndex { inputFile, idx ->
	tasks.register("OLDdominifyappJs${idx}", com.eriwen.gradle.js.tasks.MinifyJsTask.class){
		source = inputFile
        String filename = inputFile.getName();
        int dotPos = filename.lastIndexOf(".");
        String extension = filename.substring(dotPos);
        String baseFilename = filename.substring(0, filename.length()-extension.length());
        dest = new File(appJsOutputFolder, baseFilename + ".min" + extension);
		// System.out.println("${inputFile} --> ${dest}")
		sourceMap = new File(appJsOutputFolder, baseFilename + ".sourcemap.json")
		closure defaultMinifyOptions
	}
}

/* Using the new NpxTask minification */
appJsInputFiles.eachWithIndex { inputFile, idx ->
    tasks.register("dominifyappJs${idx}", NpxTask) {
        dependsOn npmInstall

        String filename = inputFile.getName()
        int dotPos = filename.lastIndexOf(".")
        String extension = filename.substring(dotPos)
        String baseFilename = filename.substring(0, filename.length() - extension.length())
        
        def outputFile = new File(appJsOutputFolder, baseFilename + ".min" + extension)
        // def sourceMapFile = new File(appJsOutputFolder, baseFilename + ".sourcemap.json")

        command = 'terser'
        args = [
            '--compress',
            '--source-map',
            '--output', 
            outputFile.absolutePath,
            '--',
            inputFile.absolutePath
        ]

        inputs.file(inputFile)
        outputs.file(outputFile)
        // outputs.file(sourceMapFile)
    }
}

/* Using the new NpxTask minification because of new language features used in yada.js */
task compressYadaJs(type: NpxTask) {
	dependsOn npmInstall
	def inputFolder = "$YadaWebLib/src/main/resources/net/yadaframework/views/yada/js";
    // Set the input and output files for your JavaScript files
    // To exclude legacy files: fileTree(dir: inputFolder, include: '**/yada*.js', exclude: '**/yada.datatablesLegacy.js')
    def inputFiles = fileTree(dir: inputFolder, include: '**/yada*.js')
    def outputFile = file("${appJsOutputFolder}/yada.min.js")
	command = 'terser'
    args = [ // "${inputFiles}",        
    	'--compress',
        '--source-map',
        '--output',
 		outputFile.absolutePath,
 		"--"
 	]
 	args.addAll(inputFiles.files.collect { it.absolutePath })
 	inputs.files(inputFiles)
 	inputs.dir(inputFolder)
    outputs.file(outputFile)
}

/**
 * Minimizza i js del configuratore v1
 */
task compressConfigurator1(type: NpxTask) {
    dependsOn npmInstall
    
    def inputFiles = project.files(
        "${configuratorDir}/js/configuration.js",
        "${configuratorDir}/js/gui2d.js"
    )
    def outputFile = file("${assetsConfiguratorDir}/configurator/gui2dconfiguration.min.js")
    
    command = 'terser'
    args = [
        '--compress',
        '--source-map',
        '--output',
        outputFile.absolutePath,
        '--'
    ]
    args.addAll(inputFiles.files.collect { it.absolutePath })
    
    inputs.files(inputFiles)
    outputs.file(outputFile)
    outputs.file("${outputFile}.map")
}

/**
 * Minimizza i js del configuratore v2.
 * E' lo stesso gui2d.js di v1 ma viene salvato in gui2d.min.js invece che combinato con configuration.js.
Rimosso perché gui2d.js non si usa più.
task compressConfigurator2(type: com.eriwen.gradle.js.tasks.MinifyJsTask) {
    source = project.files("${configuratorDir}/js/gui2d.js") 
    dest = file("${assetsConfiguratorDir}/configuratorb/gui2d.min.js")
    sourceMap = file("${assetsConfiguratorDir}/configuratorb/gui2d.sourcemap.json")
    closure defaultMinifyOptions
}
 */

/**
 * Minimizza i js del configuratore v2
 */
// Non lo facciamo perché setup.*.js è potenzialmente un template thymeleaf che si corromperebbe minimizzandolo
// task compressConfigurator2(type: com.eriwen.gradle.js.tasks.MinifyJsTask) {
//     source = project.files("${configuratorDir}/js/gui2d.js", "${configuratorBDir}/src/main/webapp/WEB-INF/views/configuratorb/setup.*.js")
//     dest = file("${assetsConfiguratorDir}/configuratorb/configuratorball.min.js")
//     sourceMap = file("${assetsConfiguratorDir}/configuratorb/configuratorball.sourcemap.json")
//     closure defaultMinifyOptions
// }

/**
 * Pulisce i js applicativi prima della minificazione
 */
task appJsOutputClean() {
    doLast {
	   delete fileTree(dir: appJsOutputFolder, include: ['**/*.min.js', '**/*.sourcemap.json'])
	}
}

/**
 * Crea le versioni min dei file js applicativi (singolarmente), dei file js yada (in un unico file) e dei file configuratore
 */
task jsCompressApp(dependsOn: [appJsOutputClean, compressYadaJs, tasks.matching { it.name.startsWith("dominifyappJs") }, 
	tasks.matching { it.name.startsWith("compressConfigurator")}]) {
}

// ant rimosso: ant.properties['assetsDir2'] = assetsConfiguratorDir

task makeWar(dependsOn: [classes, jsCompressApp, incrementBuild], type: War) {
	archiveFileName=localWarName
	// exclude('robots.txt')
	exclude('META-INF/context.xml')
	exclude('**/persistence.xml') // Non va
	exclude('META-INF/persistence.xml') // Non va
	processResources {
		exclude('**/conf.webapp.dev.xml')
		exclude('**/logback-personal.*')
		exclude('**/logback.xml')
		exclude('META-INF/persistence.xml') // Non va
	}
	if (env=='col') {
		from("/env/col/conf.webapp.col.xml") {
			into "WEB-INF/classes"
		}
	}
	if (env=='evo') {
		from("/env/evo/conf.webapp.evo.xml") {
			into "WEB-INF/classes"
		}
	}
	from("/env/${env}/context.xml") {
		into "META-INF"
	}
	from("/env/${env}/logback.xml"){
		into "WEB-INF/classes"
	}
	from("/env/${env}/robots.txt")
	// from("/src/main/webapp/res/favicon/favicon.ico") // Just because it's requested by non-compliant browsers
	//
	// Configurator section
	//
	from("$configuratorDir/configurator") {
		into "WEB-INF/views/configurator"
	}
	from("$assetsConfiguratorDir/configurator") {
		into "/res/js/configurator"
	}
	from("$configuratorDir/css/configurator.min.css") {
		into "/res/css/configurator"
	}
	from("$configuratorDir/css/finalPdf.min.css") {
		into "/res/css/configurator"
	}
	from("$configuratorDir/css/loginModal.min.css") {
		into "/res/css/configurator"
	}
	from("$configuratorDir/css/controls") {
		into "/res/css/configurator/controls"
	}
	from("$configuratorDir/css/configuratore-icons/fonts") {
		into "/res/css/configurator/configuratore-icons/fonts"
	}
	from("$configuratorDir/images") {
		into "/res/images/configurator"
	}
	from("$configuratorDir/js/app.min.js") {
		into "/res/js/configurator"
	}
	from("$configuratorDir/js/extractData.min.js") {
		into "/res/js/configurator"
	}
	from("$configuratorDir/static") {
		include 'vendor*'
		include 'html2canvas*'
		include 'jspdf*'
		into "/static/configurator"
	}
	//
	// ConfiguratorB section
	//
	from("$configuratorBDir/js") {
	   include 'app.*.min.js'
	   into "/res/js/configuratorb"
	}
	// from("$assetsConfiguratorDir/configuratorb") {
	// 	into "/res/js/configuratorb"
	// }
	// from("$configuratorBDir/static") {
	//	include 'vendor*'
	//	include 'html2canvas*'
	//	include 'jspdf*'
	//	into "/static/configuratorb"
	// }
	from("$configuratorBDir/src/main/webapp/WEB-INF/views/configuratorb") {
		into "WEB-INF/views/configuratorb"
	}
	doLast { 
		new File(workDir).deleteDir()
	}
}

// makeWar.finalizedBy appJsOutputClean

task deployWar(dependsOn: [makeWar]) {
	doLast {
		if ((env!='prod18' && env!='prod20') || confirm("Overwrite existing application on ${env}?")) {
			println "Sending files to ${env}..."
			ssh.run {
				session(remotes.deployHost) {
					put from: "$projectDir/env/${env}/bin/deploy.sh", into: "/srv/${acroenv}/bin"
					put from: warPath, into: "/srv/${acroenv}/deploy/$remoteWarName"
				}
			}
			println "Running deploy script..."
			ssh.run {
				session(remotes.deployHost) {
					execute """chmod a+x /srv/${acroenv}/bin/deploy.sh && sudo /srv/${acroenv}/bin/deploy.sh /srv/${acroenv}/deploy/${remoteWarName} ${tomcatbase} ${config.server.host} &&
					mv /srv/${acroenv}/bin/deploy.sh /srv/${acroenv}/bin/deploy.done"""
				}
			}
			delete warPath
			println "Deploy of ${remoteWarName} done."
			// Non abilito la creazione del tag perché prima bisognerebbe committare il build.properties e altri file cambiati
			// ma per ora non voglio farlo in automatico...
			// createReleaseTag()
		} else {
			println "Aborted."
		}
	}
}

private void createReleaseTag() {
    def tagName = env.replaceFirst("prod20", "prod").toUpperCase()
    try {
        runCommands("git", "tag", "-f", tagName)
    } catch (Exception e) {
        println(e.message)
    }
    try {
        runCommands("git", "push", "-f", "origin", tagName)
    } catch (Exception e) {
        println(e.message)
    }
    runCommands("git", "status")
    //runCommands("git", "tag", tagName)
}

private String runCommands(String... commands) {
	def processBuilder=new ProcessBuilder(commands)
	processBuilder.directory(project.file('.').getParentFile()) // Up one folder
    def process = processBuilder.redirectErrorStream(true).start()
    process.waitFor()
    def result = ''
    process.inputStream.eachLine { result += it + '\n' }
    def errorResult = process.exitValue() == 0
    if (!errorResult) {
        throw new IllegalStateException(result)
    }
    return result
}

task copyReportsForArchiving(type: Copy) {
from "${layout.buildDirectory.get().asFile}/reports/my-report.pdf", "src/docs/manual.pdf"
into "${layout.buildDirectory.get().asFile}/toArchive"
}

task buildDxfExporter(type: Exec) {
    workingDir dxfConverterProjectDir
    commandLine "cmd.exe", "/c", "buildAll.bat"
}

task deployDxfExporterDev(dependsOn: [buildDxfExporter]) {
    // I build the DXF Exporter once when copying to local dev
    // so it is already built when releasing to ubuntu
    doLast {
        def platformFolder = "win-x64"
        def destFolder = "C:/srv/$acroenv/bin/dxfExporter"
        delete fileTree(dir: destFolder, include: "**/*")
        // new File(${destFolder}).deleteDir()
        copy {
            from "${dxfConverterBinDir}/${platformFolder}"
            into destFolder
        }
        println "New version copied to ${destFolder}"
    }
}

task deployDxfExporter() {
    doLast {
        if (env!='prod20' || confirm("Overwrite existing DxfExporter on ${env}?")) {
            def destFolder = "/srv/$acroenv/bin/dxfExporter"
            def deployFolder = "/srv/$acroenv/deploy/dxfExporter_${getTimestamp()}"
            def platformFolder = "ubuntu.22.04-x64"
            ssh.run {
                session(remotes.deployHost) {
                    println "Transferring new files..."
                    execute "mkdir ${deployFolder}"
                    put from: "${dxfConverterBinDir}/${platformFolder}", into: deployFolder
                    println "Replacing old files..."
                    execute """sudo chown -R tomcat8 $deployFolder &&
                               sudo chmod a+x $deployFolder/$platformFolder/DxfExporter &&
                               sudo mv $destFolder ${destFolder}_old &&
                               sudo mv $deployFolder/$platformFolder $destFolder &&
                               sudo rm -rf ${destFolder}_old"""
                    remove deployFolder
                }
            }
            println "Deploy done."
        } else {
            println "Aborted."
        }
    }
    
}
